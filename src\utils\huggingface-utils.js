import config from '../config.js';
import { info, error } from './logger.js';

/**
 * 检查 HuggingFace 配置是否完整
 * @returns {boolean} 配置是否完整
 */
export function checkHuggingFaceConfig() {
  const { token, spaces } = config.huggingface;
  
  if (!token) {
    error('HuggingFace token 未配置，请设置环境变量 HF_TOKEN');
    return false;
  }
  
  if (!spaces || spaces.length === 0) {
    error('HuggingFace spaces 列表为空');
    return false;
  }
  
  return true;
}

/**
 * 重启单个 HuggingFace Space
 * @param {string} space - Space 名称
 * @returns {Promise<boolean>} 重启是否成功
 */
async function restartSpace(space) {
  const { token } = config.huggingface;
  const url = `https://huggingface.co/api/spaces/godmailyi/${space}/restart?factory=true`;
  
  try {
    info(`正在重启 HuggingFace Space: ${space}...`);
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: null
    });
    
    if (response.ok) {
      info(`✅ Space ${space} 重启成功`);
      return true;
    } else {
      const errorText = await response.text();
      error(`❌ Space ${space} 重启失败: ${response.status} ${response.statusText}`);
      error(`响应内容: ${errorText}`);
      return false;
    }
  } catch (err) {
    error(`❌ Space ${space} 重启时发生错误:`, err.message);
    return false;
  }
}

/**
 * 重启所有配置的 HuggingFace Spaces
 * @returns {Promise<boolean>} 所有空间是否都重启成功
 */
export async function restartAllSpaces() {
  if (!checkHuggingFaceConfig()) {
    return false;
  }
  
  const { spaces } = config.huggingface;
  info(`开始重启 ${spaces.length} 个 HuggingFace Spaces...`);
  
  const results = [];
  
  // 循环重启所有空间
  for (const space of spaces) {
    const success = await restartSpace(space);
    results.push(success);
    
    // 在重启之间添加短暂延迟，避免请求过于频繁
    if (spaces.indexOf(space) < spaces.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  const successCount = results.filter(result => result).length;
  const totalCount = results.length;
  
  if (successCount === totalCount) {
    info(`🎉 所有 ${totalCount} 个 HuggingFace Spaces 重启成功`);
    return true;
  } else {
    error(`⚠️  ${totalCount} 个 Spaces 中有 ${successCount} 个重启成功，${totalCount - successCount} 个失败`);
    return false;
  }
}
