import { Hono } from 'hono';
import { serve } from '@hono/node-server';
import { serveStatic } from '@hono/node-server/serve-static';
import { getRecentLogs, clearLogFile } from './utils/logger.js';
import { info as log, error as logError } from './utils/logger.js';
import config from './config.js';

const app = new Hono();

// 静态文件服务 - 提供 screenshots 目录
app.use('/screenshots/*', serveStatic({ root: './' }));

// 静态 HTML 页面
const indexHTML = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CloudStudio Runner - 日志查看器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2em;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .controls {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }
        .controls label {
            font-weight: bold;
        }
        .controls select, .controls button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .controls button {
            background: #667eea;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }
        .controls button:hover {
            background: #5a6fd8;
        }
        .log-container {
            padding: 20px;
            max-height: 600px;
            overflow-y: auto;
        }
        .log-entry {
            margin-bottom: 8px;
            padding: 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            border-left: 3px solid #ddd;
        }
        .log-entry.info {
            background-color: #f8f9fa;
            border-left-color: #28a745;
        }
        .log-entry.error {
            background-color: #fff5f5;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .log-entry.warn {
            background-color: #fffbf0;
            border-left-color: #ffc107;
            color: #856404;
        }
        .timestamp {
            color: #6c757d;
            font-weight: bold;
        }
        .level {
            font-weight: bold;
            margin: 0 8px;
        }
        .level.info { color: #28a745; }
        .level.error { color: #dc3545; }
        .level.warn { color: #ffc107; }
        .message {
            word-break: break-word;
        }
        .status {
            padding: 10px 20px;
            background: #e9ecef;
            border-top: 1px solid #ddd;
            font-size: 14px;
            color: #6c757d;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        .empty {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            font-style: italic;
        }
        .screenshot-section {
            padding: 20px;
            border-bottom: 1px solid #eee;
            text-align: center;
        }
        .screenshot-section h2 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.2em;
        }
        .screenshot-section img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 CloudStudio Runner</h1>
            <p>实时日志查看器 - 监控自动化任务执行状态</p>
        </div>

        <div class="controls">
            <label for="lines">显示行数:</label>
            <select id="lines">
                <option value="50">50 行</option>
                <option value="100" selected>100 行</option>
                <option value="200">200 行</option>
                <option value="500">500 行</option>
                <option value="1000">1000 行</option>
            </select>

            <button onclick="refreshLogs()">🔄 刷新日志</button>
            <button onclick="toggleAutoRefresh()">⏱️ 自动刷新</button>
            <button onclick="clearLogs()">🗑️ 清空显示</button>
        </div>

        <div class="log-container" id="logContainer">
            <div class="loading">正在加载日志...</div>
        </div>

        <div class="status" id="status">
            准备就绪
        </div>
        <div class="screenshot-section">
            <h2>📸 最新截图</h2>
            <img src="/screenshots/scheduler.png" alt="Scheduler Screenshot" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
            <div style="display: none; color: #6c757d; font-style: italic;">截图文件不存在或无法加载</div>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;
        let isAutoRefresh = false;

        async function fetchLogs() {
            try {
                const lines = document.getElementById('lines').value;
                const response = await fetch(\`/api/logs?lines=\${lines}\`);
                const data = await response.json();

                if (data.success) {
                    displayLogs(data.logs);
                    updateStatus(\`已加载 \${data.logs.length} 条日志 - \${new Date().toLocaleString()}\`);
                } else {
                    updateStatus('获取日志失败: ' + data.error);
                }
            } catch (error) {
                updateStatus('网络错误: ' + error.message);
            }
        }

        function displayLogs(logs) {
            const container = document.getElementById('logContainer');

            if (logs.length === 0) {
                container.innerHTML = '<div class="empty">暂无日志数据</div>';
                return;
            }

            const logHTML = logs.map(log => {
                const match = log.match(/\\[(.*?)\\]\\s*\\[(.*?)\\]\\s*(.*)/);
                if (match) {
                    const [, timestamp, level, message] = match;
                    const levelClass = level.toLowerCase();
                    return \`
                        <div class="log-entry \${levelClass}">
                            <span class="timestamp">\${timestamp}</span>
                            <span class="level \${levelClass}">[\${level}]</span>
                            <span class="message">\${message}</span>
                        </div>
                    \`;
                } else {
                    return \`
                        <div class="log-entry">
                            <span class="message">\${log}</span>
                        </div>
                    \`;
                }
            }).join('');

            container.innerHTML = logHTML;
            container.scrollTop = container.scrollHeight;
        }

        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        function refreshLogs() {
            fetchLogs();
        }

        function toggleAutoRefresh() {
            const button = event.target;

            if (isAutoRefresh) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                isAutoRefresh = false;
                button.textContent = '⏱️ 自动刷新';
                updateStatus('自动刷新已停止');
            } else {
                autoRefreshInterval = setInterval(fetchLogs, 5000);
                isAutoRefresh = true;
                button.textContent = '⏹️ 停止刷新';
                updateStatus('自动刷新已启动 (每5秒)');
            }
        }

        async function clearLogs() {
            try {
                updateStatus('正在清空日志...');

                const response = await fetch('/api/clear-logs', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    document.getElementById('logContainer').innerHTML = '<div class="empty">日志已清空</div>';
                    updateStatus('日志文件已清空 - ' + new Date().toLocaleString());
                } else {
                    updateStatus('清空日志失败: ' + data.error);
                }
            } catch (error) {
                updateStatus('清空日志时发生网络错误: ' + error.message);
            }
        }

        // 页面加载时获取日志
        document.addEventListener('DOMContentLoaded', fetchLogs);
    </script>
</body>
</html>
`;

// 路由定义
app.get('/', (c) => {
    return c.html(indexHTML);
});

// API 路由 - 获取日志
app.get('/api/logs', (c) => {
    try {
        const lines = parseInt(c.req.query('lines') || '100');
        const logs = getRecentLogs(lines);

        return c.json({
            success: true,
            logs: logs,
            count: logs.length,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logError('获取日志API错误:', error);
        return c.json({
            success: false,
            error: error.message
        }, 500);
    }
});

// API 路由 - 清空日志
app.post('/api/clear-logs', (c) => {
    try {
        const success = clearLogFile();

        if (success) {
            log('日志文件已通过Web界面清空');
            return c.json({
                success: true,
                message: '日志文件已清空',
                timestamp: new Date().toISOString()
            });
        } else {
            return c.json({
                success: false,
                error: '清空日志文件失败'
            }, 500);
        }
    } catch (error) {
        logError('清空日志API错误:', error);
        return c.json({
            success: false,
            error: error.message
        }, 500);
    }
});

// API 路由 - 系统状态
app.get('/api/status', (c) => {
    return c.json({
        success: true,
        status: 'running',
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        timestamp: new Date().toISOString(),
        config: {
            webideUrl: config.webideUrl,
            schedulerInterval: config.schedulerInterval,
            headless: config.browserOptions.headless
        }
    });
});

// 健康检查
app.get('/health', (c) => {
    return c.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 启动服务器
const port = process.env.PORT || 7860;

async function startServer() {
    try {
        log(`启动 Web 服务器，端口: ${port}`);
        log(`访问地址: http://localhost:${port}`);

        serve({
            fetch: app.fetch,
            port: port,
        });

        log('Web 服务器启动成功');
    } catch (error) {
        logError('启动 Web 服务器失败:', error);
        process.exit(1);
    }
}

// 如果直接运行此文件，启动服务器
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const scriptPath = path.resolve(process.argv[1]);

if (path.resolve(__filename) === scriptPath) {
    startServer();
}

export { app, startServer };
