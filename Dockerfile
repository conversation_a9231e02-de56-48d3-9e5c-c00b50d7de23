# 基础镜像：使用 Node.js 20 的 Alpine Linux 版本
FROM node:20-alpine

# 设置工作目录
WORKDIR /app

# 配置腾讯云 APK 源（加速国内下载）
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.cloud.tencent.com/g' /etc/apk/repositories

# 安装系统依赖
RUN apk add --no-cache \
    # 基本构建工具
    python3 \
    make \
    g++ \
    # Playwright 依赖
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont \
    # 其他依赖
    gcompat

# 设置 Playwright 的环境变量
ENV PLAYWRIGHT_BROWSERS_PATH=/usr/bin
ENV PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1
ENV PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH=/usr/bin/chromium-browser
ENV PLAYWRIGHT_SKIP_BROWSER_VALIDATION=1

# 复制 package.json 和 package-lock.json
COPY package*.json ./

# 复制应用程序文件
COPY src/ ./src/

# 复制 cookies.json 文件（如果存在）
# 使用通配符语法，如果文件不存在则跳过，不会报错
COPY cookies.json* ./

# 安装 Node.js 依赖
RUN npm ci --only=production

# 设置非 root 用户（安全最佳实践）
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# 创建 screenshots 和 logs 目录，并设置正确的权限
RUN mkdir -p /app/screenshots && \
    mkdir -p /app/logs && \
    chown -R nodejs:nodejs /app && \
    chmod -R 777 /app/screenshots && \
    chmod -R 777 /app/logs

# 切换到非 root 用户
USER nodejs

# 暴露端口 7860 用于 Web 服务器
EXPOSE 7860

# 设置默认命令 - 启动 Web 服务器
CMD ["npm", "start"]