import { info, error } from './logger.js';
import config from '../config.js';

/**
 * Cloudflare KV 工具函数
 * 用于上传和下载 cookies 到 Cloudflare KV 存储
 */

/**
 * 检查 Cloudflare KV 配置是否完整
 * @returns {boolean} 配置是否完整
 */
export function checkCloudflareKVConfig() {
  const requiredEnvVars = ['CF_ACCOUNT_ID', 'CF_NAMESPACE_ID', 'CF_API_TOKEN'];
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    error(`缺少 Cloudflare KV 配置环境变量: ${missingVars.join(', ')}`);
    return false;
  }

  return true;
}

/**
 * 上传 cookies 到 Cloudflare KV
 * @param {Array} cookies - cookies 数组
 * @returns {Promise<boolean>} 上传是否成功
 */
export async function uploadCookiesToKV(cookies) {
  if (!checkCloudflareKVConfig()) {
    return false;
  }

  try {
    const { accountId, namespaceId, apiToken, cookieKey } = config.cloudflareKV;

    // 构建 API URL
    const url = `https://api.cloudflare.com/client/v4/accounts/${accountId}/storage/kv/namespaces/${namespaceId}/values/${cookieKey}`;

    // 准备请求数据
    const cookiesJson = JSON.stringify(cookies);

    info(`正在上传 ${cookies.length} 个 cookies 到 Cloudflare KV...`);

    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${apiToken}`,
        'Content-Type': 'application/json'
      },
      body: cookiesJson
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const result = await response.json();

    if (result.success) {
      info(`成功上传 cookies 到 Cloudflare KV，键名: ${cookieKey}`);
      return true;
    } else {
      error('上传 cookies 到 Cloudflare KV 失败:', result.errors);
      return false;
    }

  } catch (err) {
    error('上传 cookies 到 Cloudflare KV 时发生错误:', err);
    return false;
  }
}

/**
 * 从 Cloudflare KV 下载 cookies
 * @returns {Promise<Array|null>} cookies 数组，失败时返回 null
 */
export async function downloadCookiesFromKV() {
  if (!checkCloudflareKVConfig()) {
    return null;
  }

  try {
    const { accountId, namespaceId, apiToken, cookieKey } = config.cloudflareKV;

    // 构建 API URL
    const url = `https://api.cloudflare.com/client/v4/accounts/${accountId}/storage/kv/namespaces/${namespaceId}/values/${cookieKey}`;

    info(`正在从 Cloudflare KV 下载 cookies，键名: ${cookieKey}...`);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiToken}`
      }
    });

    if (response.status === 404) {
      info(`Cloudflare KV 中未找到键名为 ${cookieKey} 的 cookies`);
      return null;
    }

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const cookiesJson = await response.text();
    const cookies = JSON.parse(cookiesJson);

    info(`成功从 Cloudflare KV 下载 ${cookies.length} 个 cookies`);
    return cookies;

  } catch (err) {
    error('从 Cloudflare KV 下载 cookies 时发生错误:', err);
    return null;
  }
}

/**
 * 删除 Cloudflare KV 中的 cookies
 * @returns {Promise<boolean>} 删除是否成功
 */
export async function deleteCookiesFromKV() {
  if (!checkCloudflareKVConfig()) {
    return false;
  }

  try {
    const { accountId, namespaceId, apiToken, cookieKey } = config.cloudflareKV;

    // 构建 API URL
    const url = `https://api.cloudflare.com/client/v4/accounts/${accountId}/storage/kv/namespaces/${namespaceId}/values/${cookieKey}`;

    info(`正在从 Cloudflare KV 删除 cookies，键名: ${cookieKey}...`);

    const response = await fetch(url, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${apiToken}`
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const result = await response.json();

    if (result.success) {
      info(`成功从 Cloudflare KV 删除 cookies，键名: ${cookieKey}`);
      return true;
    } else {
      error('从 Cloudflare KV 删除 cookies 失败:', result.errors);
      return false;
    }

  } catch (err) {
    error('从 Cloudflare KV 删除 cookies 时发生错误:', err);
    return false;
  }
}
