import config from './config.js';
import { fileURLToPath } from 'url';
import path from 'path';

import { createBrowser, createContextAndPage, navigateToWebIDE, executeCommandFlow } from './utils/webide-utils.js';
import { info, error } from './utils/logger.js';

async function executeCommand() {

  let browser;
  let context;
  try {
    // 创建浏览器实例
    browser = await createBrowser();

    // 创建上下文和页面
    const session = await createContextAndPage(browser, config.cookieFile, config.cookiesFromEnv, config.cloudflareKV.cookieKey);
    context = session.context;
    const page = session.page;

    // 导航到WebIDE页面
    await navigateToWebIDE(page);

    // 执行命令流程
    const success = await executeCommandFlow(page, 'screenshot');

    // 保持浏览器打开一段时间以便查看结果
    if (!config.browserOptions.headless) {
      info('浏览器将保持打开5秒以便查看结果...');
      await page.waitForTimeout(5000);
    }

  } catch (err) {
    error('执行命令过程中发生错误:', err);
  } finally {
    // 确保正确释放资源
    if (context) {
      try {
        await context.close();
        info('上下文已关闭');
      } catch (closeErr) {
        error('关闭上下文时出错:', closeErr);
      }
    }

    if (browser) {
      try {
        await browser.close();
        info('浏览器已关闭');
      } catch (closeErr) {
        error('关闭浏览器时出错:', closeErr);
      }
    }
  }
}

// 运行命令执行脚本
const __filename = fileURLToPath(import.meta.url);
const scriptPath = path.resolve(process.argv[1]);

if (path.resolve(__filename) === scriptPath) {
  executeCommand().catch(error);
}

export { executeCommand };
