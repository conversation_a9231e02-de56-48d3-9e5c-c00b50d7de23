import config from './config.js';
import { fileURLToPath } from 'url';
import path from 'path';

import { createBrowserSession, navigateToWebIDE, executeCommandFlow } from './utils/webide-utils.js';
import { info, error } from './utils/logger.js';

async function executeCommand() {

  let browser;
  try {
    // 创建浏览器会话
    const { browser: browserInstance, page } = await createBrowserSession(config.cookieFile, config.cookiesFromEnv, config.cloudflareKV.cookieKey);
    browser = browserInstance;

    // 导航到WebIDE页面
    await navigateToWebIDE(page);

    // 执行命令流程
    const success = await executeCommandFlow(page, 'screenshot');

    // 保持浏览器打开一段时间以便查看结果
    if (!config.browserOptions.headless) {
      info('浏览器将保持打开5秒以便查看结果...');
      await page.waitForTimeout(5000);
    }

  } catch (err) {
    error('执行命令过程中发生错误:', err);
  } finally {
    if (browser) {
      await browser.close();
      info('浏览器已关闭');
    }
  }
}

// 运行命令执行脚本
const __filename = fileURLToPath(import.meta.url);
const scriptPath = path.resolve(process.argv[1]);

if (path.resolve(__filename) === scriptPath) {
  executeCommand().catch(error);
}

export { executeCommand };
