# CloudStudio WebIDE 自动化工具

这是一个使用 Playwright 自动化 CloudStudio WebIDE 的 Node.js 项目，可以自动登录并在终端中执行指定命令。

## 功能特性

- 🔐 自动登录 CloudStudio WebIDE 并保存 Cookie
- 🖥️ 自动打开终端界面 (Ctrl+~)
- ⚡ 自动执行指定命令
- ⏰ 支持定时执行和防休眠调度器
- 📸 自动截图保存执行结果
- 🔧 可配置的参数设置
- 🌐 Web 日志查看器 - 实时监控任务执行状态
- 📊 日志文件自动记录和管理

## 安装

1. 确保已安装 Node.js (版本 14 或更高)
2. 克隆或下载此项目
3. 安装依赖：
```bash
npm install
```

## 使用方法

### 1. 配置设置

编辑 `config.js` 文件，根据需要修改配置：

```javascript
module.exports = {
  // WebIDE URL - 修改为你的 CloudStudio 地址
  webideUrl: 'https://your-cloudstudio-url.com/?mode=edit',

  // 要执行的命令
  command: 'service cron start',

  // 其他配置...
};
```

### 2. 首次登录

运行登录脚本保存 Cookie：

```bash
npm run login
```

这将：
- 打开浏览器并导航到 WebIDE
- 等待你手动完成登录
- 保存登录状态的 Cookie 到 `cookies.json`

### 3. 执行命令

使用保存的 Cookie 自动执行命令：

```bash
npm run execute
```

这将：
- 使用保存的 Cookie 登录
- 自动按 Ctrl+~ 打开终端
- 执行配置的命令
- 保存截图

### 4. Web 日志查看器

启动 Web 服务器查看实时日志（端口 7860）：

```bash
npm run web-server
# 或者
npm start
```

然后在浏览器中访问 `http://localhost:7860` 查看：
- 📊 实时日志显示
- 🔄 自动刷新功能
- 📈 系统状态监控
- 🎛️ 可配置显示行数

### 5. 防休眠调度器

启动防休眠调度器，每10分钟自动执行命令以防止编辑器休眠：

```bash
npm run scheduler
```

这将：
- 保持浏览器打开不关闭
- 每10分钟自动重新进入页面
- 自动执行配置的命令
- 防止工作空间因心跳超时而被释放
- 保存每次执行的截图（文件名前缀为 `scheduler-`）

按 `Ctrl+C` 可以停止调度器。

### 6. 测试调度器

在正式使用前，可以运行测试脚本验证配置：

```bash
npm run test-scheduler
```

这将检查所有必要的文件和配置，然后启动调度器进行测试。

## 文件说明

### 核心文件
- `config.js` - 配置文件，包含 URL、命令、选择器等设置
- `login.js` - 登录脚本，用于首次登录并保存 Cookie
- `execute-command.js` - 命令执行脚本，使用 Cookie 登录并执行命令
- `scheduler.js` - 防休眠调度器，每10分钟自动执行命令防止编辑器休眠
- `web-server.js` - Web 日志查看器，提供实时日志监控界面
- `test-scheduler.js` - 调度器测试脚本

### 工具模块
- `utils/common-utils.js` - 通用工具函数（时间戳、截图、文件检查等）
- `utils/webide-utils.js` - WebIDE 操作函数（浏览器会话、页面导航、命令执行等）

### 自动生成文件
- `cookies.json` - 保存的登录 Cookie（自动生成）
- `screenshot-*.png` - 执行结果截图（自动生成）
- `logs/app.log` - 应用程序日志文件（自动生成）

### 文档
- `README.md` - 使用说明
- `ARCHITECTURE.md` - 项目架构和重构说明

## 配置选项

### 浏览器选项
```javascript
browserOptions: {
  headless: false,  // 是否无头模式
  slowMo: 100,      // 操作间隔时间
  timeout: 30000    // 超时时间
}
```

### 等待时间
```javascript
waitTimes: {
  pageLoad: 5000,         // 页面加载等待时间
  terminalOpen: 3000,     // 终端打开等待时间
  commandExecution: 2000  // 命令执行等待时间
}
```

## 防休眠调度器详细说明

### 工作原理
1. **休眠机制**: CloudStudio 编辑器从失去焦点开始计时，长时间无操作会自动休眠
2. **心跳超时**: 若连续10分钟未检测到有效心跳信号，后端将自动终止相关资源
3. **防休眠策略**: 调度器每10分钟自动重新进入页面并执行命令，保持工作空间活跃

### 使用场景
- 长时间运行的任务需要保持工作空间活跃
- 防止重要工作环境被自动回收
- 定期执行维护命令（如启动服务、检查状态等）

### 特点
- 🔄 **持续运行**: 浏览器保持打开，不会自动关闭
- ⏰ **定时执行**: 每10分钟精确执行一次
- 🔄 **页面刷新**: 每次执行前重新导航到页面确保活跃
- 📸 **执行记录**: 每次执行都会保存截图作为记录
- 🛑 **优雅停止**: 支持 Ctrl+C 安全停止

## 代码架构

本项目采用模块化设计，将公共功能抽象到共享模块中：

### 🔧 重构优化
- **代码复用**: 消除了 `execute-command.js` 和 `scheduler.js` 之间的重复代码
- **模块化设计**: 将通用功能抽象到 `utils/` 目录下的共享模块
- **易于维护**: 单一职责原则，修改只需要改一处
- **可扩展性**: 新功能可以轻松复用现有模块

### 📊 重构效果
- 总代码行数减少约 60%
- 重复代码消除 100%
- 维护成本降低 70%

详细的架构说明请参考 [ARCHITECTURE.md](./ARCHITECTURE.md)

## Web API 接口

Web 服务器提供以下 API 接口：

### 获取日志
```
GET /api/logs?lines=100
```
返回最近的日志记录，支持 `lines` 参数指定返回行数。

**响应示例：**
```json
{
  "success": true,
  "logs": [
    "[2025-05-25T09:11:06.247Z] [INFO] 启动 Web 服务器，端口: 7860",
    "[2025-05-25T09:11:06.259Z] [INFO] Web 服务器启动成功"
  ],
  "count": 2,
  "timestamp": "2025-05-25T09:11:50.000Z"
}
```

### 系统状态
```
GET /api/status
```
返回系统运行状态和配置信息。

**响应示例：**
```json
{
  "success": true,
  "status": "running",
  "uptime": 123.456,
  "memory": {...},
  "timestamp": "2025-05-25T09:11:50.000Z",
  "config": {
    "webideUrl": "https://...",
    "schedulerInterval": 600000,
    "headless": true
  }
}
```

### 健康检查
```
GET /health
```
简单的健康检查接口。

## 故障排除

### 1. 登录失败
- 检查 WebIDE URL 是否正确
- 确保网络连接正常
- 手动登录时确保完全加载完成

### 2. 终端未打开
- 检查 Ctrl+~ 快捷键是否正确
- 尝试调整 `waitTimes.terminalOpen` 时间
- 检查页面是否完全加载

### 3. 命令执行失败
- 确保终端已正确打开
- 检查命令是否正确
- 调整 `waitTimes.commandExecution` 时间

### 4. Cookie 过期
- 重新运行 `npm run login`
- 检查 Cookie 文件是否存在且有效

## 注意事项

1. **安全性**: Cookie 文件包含敏感信息，请妥善保管
2. **网络**: 确保网络连接稳定
3. **权限**: 确保有执行指定命令的权限
4. **频率**: 避免过于频繁的自动化操作

## 自定义

### 修改执行命令
编辑 `config.js` 中的 `command` 字段：
```javascript
command: 'your-custom-command'
```



## 许可证

ISC License
