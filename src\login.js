import { chromium } from 'playwright';
import fs from 'fs';
import config from './config.js';
import { fileURLToPath } from 'url';
import path from 'path';
import { loadCookies } from './utils/common-utils.js';
import { info, error } from './utils/logger.js';
import { uploadCookiesToKV, checkCloudflareKVConfig } from './utils/cloudflare-kv-utils.js';
import { restartAllSpaces, checkHuggingFaceConfig } from './utils/huggingface-utils.js';

async function login() {
  info('启动浏览器...');
  const browser = await chromium.launch(config.browserOptions);
  const context = await browser.newContext();

  const cookies = await loadCookies(config.cookieFile, config.cookiesFromEnv, undefined);  //本地登录不使用cfCookie
  await context.addCookies(cookies);

  const page = await context.newPage();

  try {
    info(`导航到登录页面:${config.webideUrl}...`);
    // 首先访问主页面，通常会重定向到登录页面
    await page.goto(config.webideUrl);

    // 等待页面加载
    await page.waitForTimeout(config.waitTimes.pageLoad);

    info('当前页面URL:', page.url());
    info('页面标题:', await page.title());

    // 检查是否已经登录（如果页面包含编辑器元素，说明已登录）
    const isLoggedIn = await page.locator(config.selectors.editor).count() > 0;

    if (isLoggedIn) {
      info('检测到已经登录状态，保存cookie...');
    } else {
      info('需要登录，请在浏览器中手动完成登录过程...');
      info('登录完成后，请按 Enter 键继续...');

      // 等待用户手动登录
      await waitForUserInput();

      // 等待登录完成，检查是否出现编辑器界面
      info('等待登录完成...');
      try {
        await page.goto(config.webideUrl);
        await page.waitForSelector(config.selectors.editor, {
          timeout: 60000
        });

      } catch (err) {
        info('未检测到编辑器界面，但继续保存cookie...');
      }
    }

    // 保存cookies
    const cookies = await context.cookies();
    fs.writeFileSync(config.cookieFile, JSON.stringify(cookies, null, 2));
    info(`Cookies已保存到 ${config.cookieFile}`);
    info(`保存了 ${cookies.length} 个cookies`);

    // 显示保存的cookie信息（仅显示名称，不显示值）
    info('保存的cookie名称:');
    cookies.forEach(cookie => {
      info(`  - ${cookie.name} (域名: ${cookie.domain})`);
    });


    // 如果配置了 Cloudflare KV，则同时上传到 KV 存储
    if (checkCloudflareKVConfig()) {
      info('检测到 Cloudflare KV 配置，正在上传 cookies...');
      const uploadSuccess = await uploadCookiesToKV(cookies);
      if (uploadSuccess) {
        info('Cookies 已成功上传到 Cloudflare KV');

        // 上传成功后，重启远程服务
        if (checkHuggingFaceConfig()) {
          info('检测到 HuggingFace 配置，正在重启远程服务...');
          const restartSuccess = await restartAllSpaces();
          if (restartSuccess) {
            info('🎉 远程服务重启成功');
          } else {
            error('⚠️  远程服务重启失败，但 cookies 已上传');
          }
        } else {
          info('未配置 HuggingFace token，跳过远程服务重启');
        }

      } else {
        error('上传 cookies 到 Cloudflare KV 失败，但本地文件已保存');
      }
    } else {
      info('未配置 Cloudflare KV，跳过远程上传');
    }



  } catch (err) {
    error('登录过程中发生错误:', err);
  } finally {
    await browser.close();
  }
}

// 等待用户输入的辅助函数
async function waitForUserInput() {
  const { default: readline } = await import('readline');
  return new Promise((resolve) => {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    rl.question('', () => {
      rl.close();
      resolve();
    });
  });
}

// 运行命令执行脚本
const __filename = fileURLToPath(import.meta.url);
const scriptPath = path.resolve(process.argv[1]);

if (path.resolve(__filename) === scriptPath) {
  login().catch(error);
}

