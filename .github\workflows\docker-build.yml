name: Deploy to HF Spaces
on:
  push:
    branches: [ master ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
        
      # 配置 Git
      - name: Configure Git
        run: |
          git config --global user.email "github-actions[bot]@users.noreply.github.com"
          git config --global user.name "github-actions[bot]"
          

      # 克隆 HF Space 仓库并复制文件
      - name: Clone HF Space
        run: |
          cd ..
          git clone https://godmailyi:${{ secrets.HF_TOKEN }}@huggingface.co/spaces/godmailyi/cloudstudio-runner hf-repo
          
          # 使用 rsync 复制文件，排除不需要的文件
          rsync -av --exclude='.git' \
                    --exclude='.github' \
                    --exclude='.gitignore' \
                    --exclude='node_modules' \
                    --exclude='README.md' \
                    $GITHUB_WORKSPACE/ hf-repo/
          
          cd hf-repo
          git add .
          git commit -m "Update from GitHub Actions"
          git push
          cd ../
          rm -rf hf-repo

      # - name: Clone HF Space
      #   run: |
      #     cd ..
      #     git clone https://godmailyi:${{ secrets.HF_TOKEN }}@huggingface.co/spaces/godmailyi/tencent-ide-runner hf-repo
          
      #     # 使用 rsync 复制文件，排除不需要的文件
      #     rsync -av --exclude='.git' \
      #               --exclude='.github' \
      #               --exclude='.gitignore' \
      #               --exclude='node_modules' \
      #               --exclude='README.md' \
      #               $GITHUB_WORKSPACE/ hf-repo/
          
      #     cd hf-repo
      #     git add .
      #     git commit -m "Update from GitHub Actions"
      #     git push