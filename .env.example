# 环境变量配置示例文件
# 复制此文件为 .env 并根据需要修改配置

# WebIDE URL - 如果设置了此环境变量，将覆盖 config.js 中的默认 webideUrl
# WEBIDE_URL=https://your-custom-webide-url.com/edit

# 调度器时间间隔（毫秒）- 如果设置了此环境变量，将覆盖 config.js 中的默认值
# 默认为 600000 毫秒（10分钟）
# SCHEDULER_INTERVAL=600000

# 浏览器无头模式 - 如果设置了此环境变量，将覆盖 config.js 中的默认值
# 默认为 true（无头模式），设置为 false 可以显示浏览器界面
# HEADLESS=false

# Cookies - 如果设置了此环境变量，将优先使用环境变量中的cookies而不是cookies.json文件
# 格式为JSON字符串，包含从浏览器导出的cookies数组
# COOKIES='[{"name":"session","value":"abc123","domain":".example.com","path":"/"}]'

# 示例：
# WEBIDE_URL=https://3e8ccf585a6c4fbd9f1aa9f05ac5e415.ap-shanghai.cloudstudio.club/?mode=edit
# SCHEDULER_INTERVAL=300000  # 5分钟
# SCHEDULER_INTERVAL=900000  # 15分钟
