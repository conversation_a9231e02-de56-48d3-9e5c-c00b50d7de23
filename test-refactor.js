// 测试重构后的代码
import { createBrowser, createContextAndPage } from './src/utils/webide-utils.js';
import config from './src/config.js';
import { info, error } from './src/utils/logger.js';

async function testRefactor() {
  let browser, context, page;
  
  try {
    info('测试重构后的代码...');
    
    // 测试创建浏览器
    browser = await createBrowser();
    info('✅ 浏览器创建成功');
    
    // 测试创建上下文和页面
    const result = await createContextAndPage(
      browser, 
      config.cookieFile, 
      config.cookiesFromEnv, 
      config.cloudflareKV.cookieKey
    );
    context = result.context;
    page = result.page;
    info('✅ 上下文和页面创建成功');
    
    // 测试页面导航
    await page.goto('https://www.google.com');
    info('✅ 页面导航成功');
    
    info('🎉 所有测试通过！重构成功！');
    
  } catch (err) {
    error('❌ 测试失败:', err);
  } finally {
    // 清理资源
    if (page) {
      await page.close();
      info('页面已关闭');
    }
    if (context) {
      await context.close();
      info('上下文已关闭');
    }
    if (browser) {
      await browser.close();
      info('浏览器已关闭');
    }
  }
}

// 运行测试
testRefactor().catch(error);
