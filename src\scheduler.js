import config from './config.js';
import { fileURLToPath } from 'url';
import path from 'path';
import { getHumanReadableTimestamp } from './utils/common-utils.js';
import { createBrowser, createContextAndPage, navigateToWebIDE, executeCommandFlow } from './utils/webide-utils.js';
import { info, error } from './utils/logger.js';

// 执行单次命令的函数
async function executeCommandOnce(page) {
  info(`[${getHumanReadableTimestamp()}] 开始执行命令...`);
  return executeCommandFlow(page, 'scheduler');
}

// 主调度器函数
async function startScheduler() {

  info(`[${getHumanReadableTimestamp()}] 启动调度器...`);
  const intervalSeconds = Math.round(config.schedulerInterval / 1000);
  info(`调度器将每${intervalSeconds}秒执行一次命令以防止编辑器休眠`);

  let browser;
  try {
    // 创建浏览器实例（只创建一次）
    browser = await createBrowser();

    // 创建初始的上下文和页面
    let { context, page } = await createContextAndPage(browser, config.cookieFile, config.cookiesFromEnv, config.cloudflareKV.cookieKey);

    // 导航到WebIDE页面
    await navigateToWebIDE(page);

    // 立即执行一次命令
    await executeCommandOnce(page);

    // 设置定时器，按配置的时间间隔执行
    const intervalId = setInterval(async () => {
      try {
        // 关闭旧的上下文和页面，释放资源
        if (context) {
          info(`[${getHumanReadableTimestamp()}] 关闭旧的上下文...`);
          await context.close();
        }

        // 每次都重新创建上下文和页面，重新加载最新的cookies
        info(`[${getHumanReadableTimestamp()}] 重新创建上下文和页面，加载最新cookies...`);
        const newSession = await createContextAndPage(browser, config.cookieFile, config.cookiesFromEnv, config.cloudflareKV.cookieKey);
        context = newSession.context;
        page = newSession.page;

        // 导航到WebIDE页面
        await navigateToWebIDE(page);

        // 执行命令
        await executeCommandOnce(page);
      } catch (err) {
        error(`[${getHumanReadableTimestamp()}] 定时任务执行失败:`, err);
      }
    }, config.schedulerInterval);

    info(`[${getHumanReadableTimestamp()}] 调度器已启动，将每${intervalSeconds}秒执行一次命令`);
    info('按 Ctrl+C 停止调度器');

    // 监听进程退出信号
    process.on('SIGINT', async () => {
      info(`\n[${getHumanReadableTimestamp()}] 收到停止信号，正在关闭调度器...`);
      clearInterval(intervalId);

      // 关闭当前的上下文
      if (context) {
        info('关闭当前上下文...');
        await context.close();
      }

      // 关闭浏览器
      if (browser) {
        info('关闭浏览器...');
        await browser.close();
      }

      info('调度器已停止，所有资源已释放');
      process.exit(0);
    });

    // 保持进程运行
    process.on('SIGTERM', async () => {
      info(`\n[${getHumanReadableTimestamp()}] 收到终止信号，正在关闭调度器...`);
      clearInterval(intervalId);

      // 关闭当前的上下文
      if (context) {
        info('关闭当前上下文...');
        await context.close();
      }

      // 关闭浏览器
      if (browser) {
        info('关闭浏览器...');
        await browser.close();
      }

      info('调度器已停止，所有资源已释放');
      process.exit(0);
    });

  } catch (err) {
    error(`[${getHumanReadableTimestamp()}] 调度器启动失败:`, err);

    // 确保在错误情况下也释放资源
    if (context) {
      try {
        await context.close();
      } catch (closeErr) {
        error('关闭上下文时出错:', closeErr);
      }
    }

    if (browser) {
      try {
        await browser.close();
      } catch (closeErr) {
        error('关闭浏览器时出错:', closeErr);
      }
    }
  }
}

// 运行调度器
const __filename = fileURLToPath(import.meta.url);
const scriptPath = path.resolve(process.argv[1]);

if (path.resolve(__filename) === scriptPath) {
  startScheduler().catch(error);
}

export { startScheduler };
