import config from './config.js';
import { fileURLToPath } from 'url';
import path from 'path';
import { getHumanReadableTimestamp } from './utils/common-utils.js';
import { createBrowserSession, navigateToWebIDE, executeCommandFlow } from './utils/webide-utils.js';
import { info, error } from './utils/logger.js';

// 执行单次命令的函数
async function executeCommandOnce(page) {
  info(`[${getHumanReadableTimestamp()}] 开始执行命令...`);
  return executeCommandFlow(page, 'scheduler');
}

// 主调度器函数
async function startScheduler() {

  info(`[${getHumanReadableTimestamp()}] 启动调度器...`);
  const intervalSeconds = Math.round(config.schedulerInterval / 1000);
  info(`调度器将每${intervalSeconds}秒执行一次命令以防止编辑器休眠`);

  let browser;
  try {
    // 创建浏览器会话
    const { browser: browserInstance, page } = await createBrowserSession(config.cookieFile, config.cookiesFromEnv, config.cloudflareKV.cookieKey);
    browser = browserInstance;

    // 导航到WebIDE页面
    await navigateToWebIDE(page);

    // 立即执行一次命令
    await executeCommandOnce(page);

    // 设置定时器，按配置的时间间隔执行
    const intervalId = setInterval(async () => {
      try {
        // 重新导航到页面以确保页面活跃
        info(`[${getHumanReadableTimestamp()}] 重新导航到WebIDE页面...`);
        await navigateToWebIDE(page);

        // 执行命令
        await executeCommandOnce(page);
      } catch (err) {
        error(`[${getHumanReadableTimestamp()}] 定时任务执行失败:`, err);
      }
    }, config.schedulerInterval);

    info(`[${getHumanReadableTimestamp()}] 调度器已启动，将每${intervalSeconds}秒执行一次命令`);
    info('按 Ctrl+C 停止调度器');

    // 监听进程退出信号
    process.on('SIGINT', async () => {
      info(`\n[${getHumanReadableTimestamp()}] 收到停止信号，正在关闭调度器...`);
      clearInterval(intervalId);
      if (browser) {
        await browser.close();
      }
      info('调度器已停止，浏览器已关闭');
      process.exit(0);
    });

    // 保持进程运行
    process.on('SIGTERM', async () => {
      info(`\n[${getHumanReadableTimestamp()}] 收到终止信号，正在关闭调度器...`);
      clearInterval(intervalId);
      if (browser) {
        await browser.close();
      }
      info('调度器已停止，浏览器已关闭');
      process.exit(0);
    });

  } catch (err) {
    error(`[${getHumanReadableTimestamp()}] 调度器启动失败:`, err);
    if (browser) {
      await browser.close();
    }
  }
}

// 运行调度器
const __filename = fileURLToPath(import.meta.url);
const scriptPath = path.resolve(process.argv[1]);

if (path.resolve(__filename) === scriptPath) {
  startScheduler().catch(error);
}

export { startScheduler };
